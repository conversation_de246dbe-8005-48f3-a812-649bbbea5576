# 规格详情页

## 页面说明

这是一个基于 uni-app 和 wot-design-uni 组件库实现的证件照规格详情页面，完全按照微信小程序源码的设计规范进行开发。

## 功能特性

### 1. 页面布局
- 自定义导航栏，支持返回功能
- 规格详情信息展示
- 拍摄指南轮播图
- 美颜开关
- 底部操作按钮

### 2. 组件使用
- **wd-icon**: 用于显示图标（返回按钮、指南图标）
- **wd-swiper**: 用于拍摄指南轮播图展示
- **wd-switch**: 用于美颜功能开关
- **wd-button**: 用于底部操作按钮

### 3. 响应式设计
- 支持安全区域适配（刘海屏等）
- 固定头部和底部布局
- 适配不同屏幕尺寸

## 页面结构

```
规格详情页
├── 自定义头部
│   ├── 返回按钮
│   ├── 页面标题
│   └── 占位符
├── 规格信息卡片
│   ├── 照片尺寸
│   ├── 照片规格
│   ├── 照片底色
│   └── 文件大小
├── 拍摄指南
│   ├── 指南图标
│   ├── 标题
│   └── 轮播图
├── 美颜开关
└── 底部操作按钮
    ├── 从相册选择
    └── 拍摄
```

## 技术实现

### 1. 分包配置
页面放置在 `pages-sub` 分包中，符合微信小程序分包大小限制的要求。

### 2. 样式设计
- 使用 SCSS 预处理器
- 采用 rpx 响应式单位
- 支持深色模式适配
- 遵循原设计的视觉规范

### 3. 交互功能
- 轮播图自动播放和手动切换
- 美颜开关状态管理
- 按钮点击事件处理
- 页面导航功能

## 使用方法

### 1. 页面导航
```javascript
// 跳转到规格详情页
uni.navigateTo({
  url: '/pages-sub/spec-detail/index'
})
```

### 2. 传递参数
```javascript
// 带参数跳转
uni.navigateTo({
  url: '/pages-sub/spec-detail/index?specId=123&type=passport'
})
```

### 3. 接收参数
页面可以通过 `onLoad` 生命周期接收参数：
```javascript
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options
  console.log('页面参数:', options)
})
```

## 自定义配置

### 1. 修改规格数据
在页面的 `detail` 响应式数据中修改：
```javascript
const detail = ref({
  widthMm: '35',    // 毫米宽度
  heightMm: '45',   // 毫米高度
  widthPx: '413',   // 像素宽度
  heightPx: '531'   // 像素高度
})
```

### 2. 更换指南图片
修改 `guideImages` 数组中的图片路径：
```javascript
const guideImages = ref([
  '/static/icon/guide1.png',
  '/static/icon/guide2.png',
  // ... 更多图片
])
```

### 3. 自定义样式
通过修改 SCSS 变量或类名来调整样式：
```scss
// 修改主色调
.btn-right {
  background: #your-color;
}

// 修改卡片样式
.info {
  border-radius: 20rpx;
  box-shadow: your-shadow;
}
```

## 注意事项

1. **图片资源**: 确保 `/static/icon/` 目录下有对应的指南图片
2. **分包大小**: 注意分包大小限制，避免超出微信小程序限制
3. **兼容性**: 已适配主流小程序平台和 H5
4. **性能优化**: 轮播图使用懒加载，避免一次性加载过多图片

## 扩展功能

可以根据需要添加以下功能：
- 规格详情数据的动态获取
- 图片预览功能
- 分享功能
- 收藏功能
- 更多自定义配置选项
